import json
from configs.lion_config import get_value
from configs.config import DEFAULT_MODEL_CONFIG
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage, ToolMessage
from langgraph.graph import StateGraph, END, START
from memory.mysql_memory import MySQLCheckpointSaver
from utils.logger import logger
from agents.state import State
from service.enhanced_memory_service import EnhancedMemoryService
from langgraph.prebuilt import ToolNode, tools_condition
from tools.base import get_all_tools, add_tools, clear_all_tools
from tools.example import calculator_plus
from tools.webSearch import init_search_tools

# 检查 OpenAI API 密钥
COMPANY_OPENAI_API_BASE = "https://aigc.sankuai.com/v1/openai/native"
COMPANY_OPENAI_API_KEY = str(get_value("xiaomei.humanrelation_openai_apikey", "1930188146638651430"))

# 初始化增强记忆服务
memory_service = EnhancedMemoryService()

# --- LangGraph 核心逻辑 ---
# 读取人力资源项目的搜索工具配置
tools_config = json.loads(str(get_value("xiaomei.humanrelation.search_service_config", "{}")))
searchTool = init_search_tools(tools_config)

clear_all_tools()
core_tools = [
    calculator_plus,
    searchTool
]
add_tools(core_tools)
tools_list = get_all_tools()
tool_node = ToolNode(tools_list)
logger.info(f"tools: {tools_list}")

def process_memory_and_context(state: State): #记忆处理和上下文增强节点
    """处理用户输入的记忆并为对话添加上下文"""
    messages = state['messages']
    if not messages:
        return state

    # 分离当前用户输入和历史消息
    last_message = messages[-1]
    user_input = last_message.content if hasattr(last_message, 'content') else str(last_message)
    
    # 筛选出AIMessage和HumanMessage作为历史，并排除最后的输入
    # 确保历史消息是干净的Q&A
    history_messages = [m for m in messages[:-1] if isinstance(m, (HumanMessage, AIMessage))]
    chat_history_str = "\n".join([f"{'User' if isinstance(m, HumanMessage) else 'Assistant'}: {m.content}" for m in history_messages])

    user_id = state.get('user_id', 'default_user')

    try:
        # 1. 提取并自动存储记忆
        memory_result = memory_service.extract_and_process_memory(user_input, user_id)
        
        # 2. 检索相关记忆为对话提供上下文
        retrieval_result = memory_service.retrieve_memory_for_conversation(user_input, user_id, 3)
        
        # 3. 构建记忆上下文
        memory_context_str = _build_memory_context(retrieval_result, memory_result)

        # 4. 获取系统角色设定
        system_prompt = str(get_value("humanrelation.default_system", "你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。"))
        
        # 5. 构建结构化的最终提示
        final_prompt = f"""system_prompt: {system_prompt}\n\nchathistory:\n{chat_history_str}\n\nmemory:\n{memory_context_str}\n\nask: {user_input}"""
        
        # 6. 将最终提示暂存，而不是修改message历史
        return {"memory_context": final_prompt.strip()}

    except Exception as e:
        logger.error(f"记忆处理失败: {str(e)}")
        # 即使失败，也要确保返回一个标准的SystemMessage，避免后续节点出错
        return {"messages": [SystemMessage(content=str(get_value("humanrelation.default_system", "你是一个乐于助人的人力资源助理。请总是使用中文进行友好和专业的回答。")))] + messages}

def call_model(state: State): #调用模型节点
    """调用 OpenAI 模型节点"""
    messages = state['messages']
    
    # 如果最后一条消息是 ToolMessage，这意味着我们正处于工具调用循环中。
    # 在这种情况下，我们必须使用完整的消息历史，以便模型能看到工具的输出。
    if messages and isinstance(messages[-1], ToolMessage):
        messages_for_model = messages
    else:
        # 否则，这是第一次调用（或非工具调用后的调用）。
        # 我们可以使用 memory_context 中的增强型提示。
        final_prompt = state.get('memory_context')
        if isinstance(final_prompt, str):
            # 使用最终提示作为本次调用的唯一输入
            messages_for_model = [HumanMessage(content=final_prompt)]
        else:
            # 如果 memory_context 因故缺失，则回退
            logger.warning("在call_model中未找到有效的final_prompt，将使用原始消息。")
            messages_for_model = messages
    
    # 打印最终发送给回答模型的内容
    logger.info("--- 向最终回答模型发送的内容 ---")
    for msg in messages_for_model:
        logger.info(f"角色: {msg.type}, 内容: {msg.content}")
    logger.info("-----------------------------")

    model_config = json.loads(str(get_value("xiaomei.humanrelation.model_config", DEFAULT_MODEL_CONFIG)))

    logger.info(f"model_config: {model_config},COMPANY_OPENAI_API_KEY: {COMPANY_OPENAI_API_KEY}")
    
    # 初始化 OpenAI 模型
    my_max_tokens = model_config.get("max_tokens", 1024)
    if "anthropic" in model_config["model_name"]:
        custom_headers = {"Authorization": f"Bearer {COMPANY_OPENAI_API_KEY}"}
        model = ChatOpenAI(model_name=model_config["model_name"], temperature=model_config["temperature"], streaming=True, openai_api_base=COMPANY_OPENAI_API_BASE, openai_api_key=COMPANY_OPENAI_API_KEY,default_headers=custom_headers,max_tokens=my_max_tokens)
    else:
        model = ChatOpenAI(
            model_name=model_config["model_name"],
            temperature=model_config["temperature"],
            streaming=True,
            openai_api_base=COMPANY_OPENAI_API_BASE,
            openai_api_key=COMPANY_OPENAI_API_KEY,
            max_tokens=my_max_tokens
        )

    # 【新增】将工具绑定到模型上，让模型知道有哪些工具可用
    model_with_tools = model.bind_tools(tools_list)

    # 调用模型
    response = model_with_tools.invoke(messages_for_model)
    
    # 返回AI响应，它将被追加到纯净的聊天历史中
    return {"messages": [response]}

def _build_memory_context(retrieval_result: dict, memory_result: dict) -> str: #构建记忆上下文
    """基于检索到的记忆构建记忆上下文部分的字符串"""
    try:
        if retrieval_result.get("result") != "success":
            return "没有检索到相关记忆。"
        
        persons = retrieval_result.get("persons", [])
        events = retrieval_result.get("events", [])
        suggestions = retrieval_result.get("suggestions", "")
        
        memory_context_parts = []
        
        # 添加人物信息（长期记忆）
        if persons:
            person_texts = []
            for person in persons[:2]:  # 最多显示2个相关人物
                person_text = f"• {person.get('canonical_name', '')}: {person.get('profile_summary', '')}"
                key_attrs = person.get('key_attributes', {})
                if key_attrs:
                    attrs_text = ", ".join([f"{k}: {v}" for k, v in key_attrs.items() if not k.endswith('_更新时间') and not k.startswith('verification')])
                    if attrs_text:
                        person_text += f"\n  关键信息: {attrs_text}"
                person_texts.append(person_text)
            if person_texts:
                memory_context_parts.append("【相关人物档案】\n" + "\n".join(person_texts))

        # 添加相关事件（短期记忆）
        if events:
            event_texts = []
            for event in events[:3]:  # 最多显示3个相关事件
                event_text = f"• {event.get('description_text', '')}"
                if event.get('location'):
                    event_text += f"\n  地点: {event.get('location')}"
                if event.get('sentiment'):
                    event_text += f"\n  情感: {event.get('sentiment')}"
                event_texts.append(event_text)
            if event_texts:
                memory_context_parts.append("【相关事件记忆】\n" + "\n".join(event_texts))
        
        # 添加对话建议
        if suggestions:
            memory_context_parts.append(f"【对话建议】\n{suggestions}")
        
        # 添加验证信息
        if memory_result.get("action_code") == "PROCESS_COMPLETE":
            persons_results = memory_result.get("payload",{}).get("persons",[])
            verification_messages = []
            for person_res in persons_results:
                verification_msg = person_res.get("verification_message","")
                if verification_msg:
                    verification_messages.append(verification_msg)
            if verification_messages:
                memory_context_parts.append("【需要确认】\n" + "\n".join(verification_messages))
        
        return "\n\n".join(memory_context_parts) if memory_context_parts else "没有检索到相关记忆。"
    except Exception as e:
        logger.error(f"构建记忆上下文失败: {str(e)}")
        return "构建记忆上下文时出错。"

# 1. 构建图
workflow = StateGraph(State)

# 2. 添加节点
workflow.add_node("memory_processor", process_memory_and_context)
workflow.add_node("agent", call_model)

workflow.add_node("tools", tool_node)

# 3. 定义图的流程
workflow.set_entry_point("memory_processor")
workflow.add_edge("memory_processor", "agent")
workflow.add_conditional_edges(
    "agent",
    tools_condition,
    {
        "tools": "tools",
        END: END
    }
)
workflow.add_edge("tools", "agent")

# 4. 设置记忆
memory = MySQLCheckpointSaver()

# 5. 编译图
graph = workflow.compile(checkpointer=memory)

# --- 流式响应生成器 ---

async def get_agent_response_stream(user_input: str, conversation_id: str, user_id: str): #流式响应生成器
    """异步生成器函数，供 FastAPI 调用"""
    # 定义 LangGraph 的标准输入格式，直接将user_id注入state
    inputs = {
        "messages": [HumanMessage(content=user_input)],
        "user_id": user_id
    }
    
    # 定义配置，将thread_id和user_id都包含在内
    config = {"configurable": {"thread_id": conversation_id, "user_id": user_id}}
    
    # 累积收到的内容，实现"叠加"输出
    accumulated = ""
    # 使用 astream_events V2 接口
    async for event in graph.astream_events(inputs, config=config, version="v2"):
        kind = event["event"]

        #logger.info(f"current event: {event}")  ##慎用，一大堆文字，会打印很多
        if kind == "on_chat_model_stream":
            content = event["data"]["chunk"].content
            if content:
                accumulated += content
                data = json.dumps({"content": accumulated, "type": "chat"}, ensure_ascii=False)
                yield f"data: {data}\n\n"

        elif kind == "on_tool_start":
            tool_name = event["name"]
            tool_input = event["data"]["input"]
            result = json.dumps({"tool_name": tool_name, "tool_input": tool_input, "status": "start"}, ensure_ascii=False)
            yield f"data: {result}\n\n"

        elif kind == "on_tool_end":
            tool_name = event["name"]
            tool_output = event["data"]["output"].content
            result = json.dumps({"tool_name": tool_name, "tool_output": tool_output, "status": "end"}, ensure_ascii=False)
            yield f"data: {result}\n\n"
